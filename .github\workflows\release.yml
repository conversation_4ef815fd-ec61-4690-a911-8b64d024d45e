name: build-release

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - pre-release
      - test
      - macos-sequoia-crash

jobs:
  build-and-publish:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: macos-latest
            name: darwin
            arch: x64
          - os: macos-latest
            name: darwin
            arch: arm64
          - os: windows-latest
            name: win32
            arch: x64
          - os: ubuntu-latest
            name: linux
            arch: x64
          - os: ubuntu-latest
            name: linux
            arch: arm64
      fail-fast: false
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v2

    - name: Decode and Install Certificate
      if: matrix.os == 'macos-latest'
      run: |
        echo ${{ secrets.MACOS_CERT }} | base64 --decode > certificate.p12
        security create-keychain -p action build.keychain
        security default-keychain -s build.keychain
        security unlock-keychain -p action build.keychain
        security import certificate.p12 -k build.keychain -P ${{ secrets.MACOS_CERT_PASSWORD }} -T /usr/bin/codesign
        security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k action build.keychain

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'

    - name: Install Dependencies
      run: npm ci

    - name: Build Application
      run: npm run build

    - name: Publish Electron App
      env:
        APPLE_ID: ${{ secrets.APPLE_ID }}
        APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
        APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
        GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
      run: npm run electron:publish -- --platform=${{ matrix.name }} --arch=${{ matrix.arch }}
