# Configuration Bitfocus Companion pour Gabin

Ce guide explique comment configurer Bitfocus Companion pour contrôler Gabin et recevoir le feedback des micros.

## Configuration de base

### 1. Ajouter une instance OSC Generic

1. Dans Bitfocus Companion, allez dans l'onglet **Connections**
2. Cliquez sur **Add connection**
3. Sélectionnez **Generic OSC**
4. Configurez :
   - **Target IP** : L'IP de votre serveur Gabin (ex: `*************`)
   - **Target Port** : Le port OSC de Gabin (par défaut `8000`)
   - **Listen Port** : Un port libre pour recevoir les feedbacks (ex: `8001`)

### 2. Actions disponibles

#### Contrôle des micros
- **Path** : `/mic/$NAME_OF_YOUR_MIC`
- **Arguments** : `1` (activer) ou `0` (désactiver)

#### Contrôle de l'autocam
- **Path** : `/autocam`
- **Arguments** : `1` (activer) ou `0` (désactiver)

#### Déclenchement de sources
- **Path** : `/source/$NAME_OF_YOUR_SOURCE`
- **Arguments** : aucun

## Configuration du feedback des micros

### 1. Enregistrement pour le feedback automatique

Pour recevoir automatiquement les changements d'état des micros :

1. Créez un bouton avec l'action **OSC Send**
2. Configurez :
   - **Path** : `/register/micFeedback`
   - **Arguments** : 
     - `************` (IP de votre Companion)
     - `8001` (port d'écoute de votre Companion)
     - `/mic/feedback` (chemin pour recevoir les feedbacks)

### 2. Interrogation du statut d'un micro spécifique

Pour demander le statut actuel d'un micro :

1. Créez un bouton avec l'action **OSC Send**
2. Configurez :
   - **Path** : `/gabin/mic-status/$NAME_OF_YOUR_MIC`
   - **Arguments** :
     - `************` (IP de votre Companion)
     - `8001` (port d'écoute de votre Companion)
     - `/mic/$NAME_OF_YOUR_MIC/status` (chemin pour recevoir la réponse)

### 3. Configuration des feedbacks

1. Dans l'onglet **Feedbacks** de votre bouton
2. Ajoutez un feedback **OSC Receive**
3. Configurez :
   - **Path** : `/mic/$NAME_OF_YOUR_MIC`
   - **Value** : `1` (pour l'état activé)
4. Définissez l'apparence du bouton quand le micro est activé

## Exemple de configuration complète

### Bouton de contrôle micro "Micro1"

**Actions :**
- **Press** : `/mic/Micro1` avec argument `1`
- **Release** : `/mic/Micro1` avec argument `0`

**Feedbacks :**
- **Path** : `/mic/Micro1`
- **Value** : `1`
- **Style** : Fond vert, texte "MICRO1 ON"

**Style par défaut :**
- Fond rouge, texte "MICRO1 OFF"

### Script d'initialisation

Créez un bouton "Init Gabin Feedback" avec ces actions :

1. `/register/micFeedback` avec arguments : `[IP_COMPANION]`, `[PORT_COMPANION]`, `/mic/feedback`
2. `/register/autocam` avec arguments : `[IP_COMPANION]`, `[PORT_COMPANION]`, `/autocam/status`
3. `/register/shot` avec arguments : `[IP_COMPANION]`, `[PORT_COMPANION]`, `/shot/current`

## Dépannage

### Les feedbacks ne fonctionnent pas
1. Vérifiez que Gabin est démarré et connecté
2. Vérifiez les adresses IP et ports
3. Assurez-vous que le bouton d'initialisation a été pressé
4. Vérifiez les logs de Gabin pour voir si les messages OSC sont reçus

### Les commandes ne fonctionnent pas
1. Vérifiez l'adresse IP et le port de Gabin
2. Vérifiez que les noms de micros correspondent exactement
3. Vérifiez que Gabin est en mode "on"

## Notes importantes

- Les noms de micros sont sensibles à la casse
- Le feedback est automatique une fois l'enregistrement effectué
- Redémarrez l'enregistrement si Gabin redémarre
- Les feedbacks sont envoyés à tous les contrôleurs enregistrés
