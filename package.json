{"name": "gabin", "productName": "<PERSON><PERSON><PERSON>", "version": "0.2.18", "description": "Automate camera switching with OBS, Bitfocus Companion and any audio api.", "author": "OneClickStudio", "homepage": "https://oneclickstudio.fr", "repository": "github:one-click-studio/gabin", "main": "./build/main/index.js", "bin": "./build/main/index.js", "scripts": {"postinstall": "patch-package && npm --prefix ./src/render ci", "typecheck": "tsc --noEmit -p tsconfig.json --composite false", "build": "shx rm -rf ./build && concurrently \"npm run build:client\" \"npm run build:server\"", "build:server": "tsc && shx cp -R ./src/resources ./build/resources", "build:client": "npm --prefix ./src/render run build && shx mkdir -p ./build/render/dist && shx cp -R ./src/render/dist ./build/render", "clean": "shx rm -rf ./out && shx rm -rf ./build && shx rm -rf ./dist && shx rm -rf ./*-dist", "preview": "npm run build && npm run electron:start", "electron:start": "electron-forge start", "electron:package": "electron-forge package", "electron:make": "electron-forge make", "electron:publish": "electron-forge publish"}, "dependencies": {"audio-manager-node": "github:one-click-studio/audio-manager-node", "deep-eql": "^4.1.3", "dotenv": "^16.0.3", "electron-squirrel-startup": "^1.0.0", "fast-xml-parser": "^4.2.0", "finalhandler": "^1.2.0", "node-osc": "^9.1.4", "obs-websocket-js": "^5.0.2", "patch-package": "^8.0.0", "rxjs": "^7.8.0", "serve-static": "^1.15.0", "simple-json-db": "^2.0.0", "socket.io": "^4.6.0"}, "devDependencies": {"@electron-forge/cli": "^7.2.0", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@electron-forge/plugin-auto-unpack-natives": "^7.2.0", "@electron-forge/publisher-github": "^7.2.0", "@types/deep-eql": "^4.0.0", "@types/finalhandler": "^1.2.0", "@types/node": "^18.13.0", "@types/node-osc": "^6.0.3", "@types/serve-static": "^1.15.0", "@types/wav": "^1.0.1", "concurrently": "^7.6.0", "cross-env": "^7.0.3", "electron": "^35.1.2", "nodemon": "^2.0.20", "shx": "^0.3.4", "ts-node": "^10.9.1", "typescript": "^5.0.3"}}