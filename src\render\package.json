{"name": "render", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env-shell \"vite --port $GABIN_CLIENT_PORT\"", "build": "vue-tsc && vite build --base=./", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.7", "klona": "^2.0.5", "rxjs": "^7.8.0", "socket.io-client": "^4.6.0", "unocss": "^0.48.0", "vue": "^3.2.45", "vue-router": "^4.1.6"}, "devDependencies": {"@types/node": "^18.13.0", "@unocss/reset": "^0.49.4", "@unocss/transformer-directives": "^0.49.4", "@vitejs/plugin-vue": "^3.2.0", "typescript": "^4.9.5", "unocss-preset-scrollbar": "^0.2.1", "vite": "^3.2.4", "vitest": "^0.27.1", "vue": "^3.2.45", "vue-tsc": "^1.0.9"}}