{
  "compilerOptions": {
    "declaration": true,
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": true,
    "jsx": "preserve",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["ESNext", "DOM"],
    "skipLibCheck": true,
    "noEmit": true,
    "composite": true,
    "allowSyntheticDefaultImports": true,
    "types": ["vite/client"],
    "baseUrl": ".",
    "paths": {
      "@src/*": ["./src/*"],
      "@types/*": ["../types/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "vite.config.ts"
  ],
}
