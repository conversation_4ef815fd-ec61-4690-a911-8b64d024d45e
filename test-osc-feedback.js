#!/usr/bin/env node

/**
 * Script de test pour vérifier le feedback OSC des micros avec Gabin
 * 
 * Usage:
 * node test-osc-feedback.js [GABIN_IP] [GABIN_PORT] [LISTEN_PORT]
 * 
 * Exemple:
 * node test-osc-feedback.js ************* 8000 8001
 */

const osc = require('node-osc');

// Configuration par défaut
const GABIN_IP = process.argv[2] || '127.0.0.1';
const GABIN_PORT = parseInt(process.argv[3]) || 8000;
const LISTEN_PORT = parseInt(process.argv[4]) || 8001;
const LOCAL_IP = '127.0.0.1';

console.log(`🎛️  Test OSC Feedback pour Gabin`);
console.log(`📡 Gabin: ${GABIN_IP}:${GABIN_PORT}`);
console.log(`👂 Écoute: ${LOCAL_IP}:${LISTEN_PORT}`);
console.log('');

// Créer le serveur pour écouter les feedbacks
const server = new osc.Server(LISTEN_PORT, LOCAL_IP, () => {
    console.log(`✅ Serveur OSC démarré sur ${LOCAL_IP}:${LISTEN_PORT}`);
    
    // Enregistrer pour recevoir les feedbacks des micros
    registerForMicFeedback();
    
    // Tester quelques commandes après un délai
    setTimeout(() => {
        testMicCommands();
    }, 2000);
});

// Écouter tous les messages OSC
server.on('message', (msg) => {
    const [address, ...args] = msg;
    console.log(`📨 Reçu: ${address}`, args.length > 0 ? args : '');
    
    // Analyser les feedbacks de micros
    if (address.startsWith('/mic/')) {
        const micName = address.split('/')[2];
        const status = args[0];
        const statusText = status === 1 ? '🟢 ON' : '🔴 OFF';
        console.log(`   🎤 ${micName}: ${statusText}`);
    }
});

server.on('error', (err) => {
    console.error('❌ Erreur serveur OSC:', err);
});

// Fonction pour s'enregistrer aux feedbacks des micros
function registerForMicFeedback() {
    console.log('📝 Enregistrement pour les feedbacks des micros...');
    
    const client = new osc.Client(GABIN_IP, GABIN_PORT);
    
    // S'enregistrer pour recevoir les feedbacks automatiques
    const registerMsg = new osc.Message('/register/micFeedback', LOCAL_IP, LISTEN_PORT, '/mic');
    client.send(registerMsg, (err) => {
        if (err) {
            console.error('❌ Erreur lors de l\'enregistrement:', err);
        } else {
            console.log('✅ Enregistrement envoyé pour les feedbacks des micros');
        }
        client.close();
    });
}

// Fonction pour tester les commandes de micros
function testMicCommands() {
    console.log('');
    console.log('🧪 Test des commandes de micros...');
    
    const testMics = ['Micro1', 'Micro2', 'Host'];
    let commandIndex = 0;
    
    const sendNextCommand = () => {
        if (commandIndex >= testMics.length * 4) {
            console.log('');
            console.log('✅ Tests terminés. Le serveur continue d\'écouter...');
            console.log('💡 Vous pouvez maintenant tester depuis Bitfocus Companion');
            console.log('🛑 Appuyez sur Ctrl+C pour arrêter');
            return;
        }
        
        const micIndex = Math.floor(commandIndex / 4);
        const actionIndex = commandIndex % 4;
        const micName = testMics[micIndex];
        
        const client = new osc.Client(GABIN_IP, GABIN_PORT);
        
        let message;
        let description;
        
        switch (actionIndex) {
            case 0:
                // Activer le micro
                message = new osc.Message(`/mic/${micName}`, 1);
                description = `Activation ${micName}`;
                break;
            case 1:
                // Demander le statut
                message = new osc.Message(`/gabin/mic-status/${micName}`, LOCAL_IP, LISTEN_PORT, `/mic/${micName}/status`);
                description = `Demande statut ${micName}`;
                break;
            case 2:
                // Désactiver le micro
                message = new osc.Message(`/mic/${micName}`, 0);
                description = `Désactivation ${micName}`;
                break;
            case 3:
                // Demander le statut à nouveau
                message = new osc.Message(`/gabin/mic-status/${micName}`, LOCAL_IP, LISTEN_PORT, `/mic/${micName}/status`);
                description = `Demande statut ${micName}`;
                break;
        }
        
        console.log(`📤 ${description}...`);
        client.send(message, (err) => {
            if (err) {
                console.error(`❌ Erreur: ${err}`);
            }
            client.close();
            
            commandIndex++;
            setTimeout(sendNextCommand, 1500);
        });
    };
    
    sendNextCommand();
}

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
    console.log('');
    console.log('🛑 Arrêt du serveur de test...');
    server.close();
    process.exit(0);
});

console.log('⏳ Démarrage du serveur de test...');
